/**
 * Database connection and configuration for Risk-App
 * Uses better-sqlite3 for local SQLite database with progressive schema discovery
 * Falls back to mock database if better-sqlite3 is not available
 */

let Database;
let useMockDatabase = false;

// Try to load better-sqlite3, fall back to mock if not available
function loadDatabase() {
  try {
    Database = require('better-sqlite3');
    console.log('✅ better-sqlite3 loaded successfully');
    return false; // Not using mock
  } catch (error) {
    console.warn('⚠️ better-sqlite3 not available, using mock database:', error.message);
    const { MockDatabase } = require('./mock-database');
    Database = MockDatabase;
    return true; // Using mock
  }
}

const path = require('path');
const { app } = require('electron');
const fs = require('fs');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.isInitialized = false;
    this.dbPath = null;
  }

  /**
   * Initialize the database connection
   * @param {boolean} isDev - Development mode flag
   */
  async initialize(isDev = false) {
    try {
      // Load database implementation (better-sqlite3 or mock)
      useMockDatabase = loadDatabase();

      // Determine database path
      const userDataPath = app ? app.getPath('userData') : './data';
      const dbDir = path.join(userDataPath, 'database');

      // Ensure database directory exists
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      this.dbPath = path.join(dbDir, useMockDatabase ? 'risk-app-mock.json' : 'risk-app.db');

      // Create database connection
      if (useMockDatabase) {
        this.db = new Database();
        await this.db.initialize(isDev);
      } else {
        try {
          this.db = new Database(this.dbPath, {
            verbose: isDev ? console.log : null, // Log SQL in development
            fileMustExist: false // Create if doesn't exist
          });

          // Configure SQLite for optimal performance
          this.configureSQLite();
        } catch (dbError) {
          console.warn('⚠️ Failed to create SQLite database, falling back to mock:', dbError.message);
          // Fall back to mock database
          useMockDatabase = true;
          const { MockDatabase } = require('./mock-database');
          Database = MockDatabase;
          this.db = new Database();
          await this.db.initialize(isDev);
          this.dbPath = path.join(dbDir, 'risk-app-mock.json');
        }
      }

      // Run migrations
      await this.runMigrations();

      this.isInitialized = true;
      console.log(`✅ Database initialized at: ${this.dbPath}`);

      return this.db;
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Configure SQLite settings for optimal performance
   */
  configureSQLite() {
    if (useMockDatabase) {
      console.log('✅ Mock Database configured');
      return;
    }

    // Enable WAL mode for better concurrency and performance
    this.db.pragma('journal_mode = WAL');

    // Set synchronous mode for better performance (still safe with WAL)
    this.db.pragma('synchronous = NORMAL');

    // Increase cache size (in KB)
    this.db.pragma('cache_size = 10000');

    // Enable foreign key constraints
    this.db.pragma('foreign_keys = ON');

    // Set temp store to memory for better performance
    this.db.pragma('temp_store = MEMORY');

    console.log('✅ SQLite configured for optimal performance');
  }

  /**
   * Run database migrations
   */
  async runMigrations() {
    try {
      // Create migrations table if it doesn't exist
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          version INTEGER UNIQUE NOT NULL,
          name TEXT NOT NULL,
          executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Get current migration version
      const currentVersion = this.getCurrentMigrationVersion();
      console.log(`📊 Current migration version: ${currentVersion}`);

      // Load and execute pending migrations
      const migrations = this.getMigrations();
      const pendingMigrations = migrations.filter(m => m.version > currentVersion);

      if (pendingMigrations.length === 0) {
        console.log('✅ Database is up to date');
        return;
      }

      console.log(`🔄 Running ${pendingMigrations.length} pending migrations...`);

      // Execute migrations in transaction
      const transaction = this.db.transaction(() => {
        for (const migration of pendingMigrations) {
          console.log(`  📝 Executing migration ${migration.version}: ${migration.name}`);
          migration.up(this.db);

          // Record migration execution
          this.db.prepare(`
            INSERT INTO migrations (version, name) VALUES (?, ?)
          `).run(migration.version, migration.name);
        }
      });

      transaction();
      console.log('✅ All migrations completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Get current migration version
   * @returns {number} Current version
   */
  getCurrentMigrationVersion() {
    try {
      const result = this.db.prepare(`
        SELECT MAX(version) as version FROM migrations
      `).get();
      return result?.version || 0;
    } catch (error) {
      // Table doesn't exist yet
      return 0;
    }
  }

  /**
   * Get all available migrations
   * @returns {Array} Migration objects
   */
  getMigrations() {
    return [
      {
        version: 1,
        name: 'initial_schema',
        up: (db) => this.migration001_initialSchema(db)
      },
      {
        version: 2,
        name: 'sync_metadata',
        up: (db) => this.migration002_syncMetadata(db)
      },
      {
        version: 3,
        name: 'operation_queue',
        up: (db) => this.migration003_operationQueue(db)
      }
    ];
  }

  /**
   * Migration 001: Initial schema based on discovered patterns
   */
  migration001_initialSchema(db) {
    // Users table (from authentication patterns)
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        server_id TEXT UNIQUE,
        email TEXT UNIQUE NOT NULL,
        name TEXT,
        role TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'conflict', 'error'))
      )
    `);

    // Deals table (from navigation and forms)
    db.exec(`
      CREATE TABLE IF NOT EXISTS deals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        server_id TEXT UNIQUE,
        name TEXT,
        status TEXT,
        loan_closing_date TEXT,
        reviewed_needed_date TEXT,
        requested_items TEXT,
        borrower_sponsor_name TEXT,
        loan_type TEXT,
        mortgage_loan_amount TEXT,
        property_name TEXT,
        property_address TEXT,
        property_type TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'conflict', 'error'))
      )
    `);

    // Reports table (from API endpoints)
    db.exec(`
      CREATE TABLE IF NOT EXISTS reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        server_id TEXT UNIQUE,
        name TEXT NOT NULL,
        link TEXT,
        description TEXT,
        date TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'conflict', 'error'))
      )
    `);

    // Consultants table (from navigation)
    db.exec(`
      CREATE TABLE IF NOT EXISTS consultants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        server_id TEXT UNIQUE,
        name TEXT,
        email TEXT,
        phone TEXT,
        specialization TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'conflict', 'error'))
      )
    `);

    console.log('✅ Initial schema created');
  }

  /**
   * Migration 002: Sync metadata tracking
   */
  migration002_syncMetadata(db) {
    db.exec(`
      CREATE TABLE IF NOT EXISTS sync_metadata (
        table_name TEXT PRIMARY KEY,
        last_sync_timestamp DATETIME,
        last_sync_id TEXT,
        total_records INTEGER DEFAULT 0,
        pending_operations INTEGER DEFAULT 0,
        last_error TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Initialize sync metadata for existing tables
    const tables = ['users', 'deals', 'reports', 'consultants'];
    const insertStmt = db.prepare(`
      INSERT OR IGNORE INTO sync_metadata (table_name, total_records)
      VALUES (?, 0)
    `);

    for (const table of tables) {
      insertStmt.run(table);
    }

    console.log('✅ Sync metadata tables created');
  }

  /**
   * Migration 003: Operation queue for offline operations
   */
  migration003_operationQueue(db) {
    db.exec(`
      CREATE TABLE IF NOT EXISTS operation_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operation_type TEXT NOT NULL CHECK (operation_type IN ('CREATE', 'UPDATE', 'DELETE')),
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        server_id TEXT,
        data TEXT, -- JSON data
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced BOOLEAN DEFAULT FALSE,
        retry_count INTEGER DEFAULT 0,
        last_error TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for better performance
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_operation_queue_synced
      ON operation_queue(synced);
    `);

    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_operation_queue_table
      ON operation_queue(table_name);
    `);

    console.log('✅ Operation queue created');
  }

  /**
   * Get database instance
   * @returns {Database} SQLite database instance
   */
  getDatabase() {
    if (!this.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) {
      this.db.close();
      this.isInitialized = false;
      console.log('✅ Database connection closed');
    }
  }

  /**
   * Get database path
   * @returns {string} Database file path
   */
  getDatabasePath() {
    return this.dbPath;
  }
}

// Export singleton instance
const databaseManager = new DatabaseManager();

module.exports = {
  DatabaseManager,
  databaseManager
};
