import React from 'react';
import { useElectron, useNetworkStatus } from '../../hooks/useElectron';

interface ElectronStatusProps {
  className?: string;
  showVersion?: boolean;
  showNetworkStatus?: boolean;
}

/**
 * Component that displays Electron-specific status information
 * Shows app version, network status, and environment info
 */
const ElectronStatus: React.FC<ElectronStatusProps> = ({
  className = '',
  showVersion = true,
  showNetworkStatus = true,
}) => {
  const { isElectron, appVersion } = useElectron();
  const isOnline = useNetworkStatus();

  if (!isElectron) {
    return null; // Don't show anything in web environment
  }

  return (
    <div className={`electron-status ${className}`}>
      {showVersion && appVersion && (
        <span className="electron-version text-muted small">
          v{appVersion}
        </span>
      )}
      
      {showNetworkStatus && (
        <span className={`network-status ms-2 ${isOnline ? 'text-success' : 'text-danger'}`}>
          <i className={`fa fa-circle ${isOnline ? 'text-success' : 'text-danger'}`} />
          <span className="ms-1 small">
            {isOnline ? 'Online' : 'Offline'}
          </span>
        </span>
      )}
    </div>
  );
};

export default ElectronStatus;
