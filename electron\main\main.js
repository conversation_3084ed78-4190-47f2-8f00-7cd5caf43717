const { app, BrowserWindow, Menu, shell, ipcMain, dialog, clipboard, session } = require('electron');
const { join, resolve } = require('path');
const { promises: fs } = require('fs');
const isDev = require('electron-is-dev');
const windowStateKeeper = require('electron-window-state');
const {
  isUrlSafe,
  validateIpcMessage,
  sanitizeFilePath,
  validatePermissionRequest,
  applySecurityHeaders
} = require('../security/security-utils');
const { databaseManager } = require('../database/database');
const { ModelFactory } = require('../database/models');

// Keep a global reference of the window object
let mainWindow;

// Database and models
let db;
let models;

// Enable live reload for Electron in development
if (isDev) {
  require('electron-reload')(__dirname, {
    electron: resolve(__dirname, '..', '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

function createWindow() {
  // Load the previous window state or set defaults
  const mainWindowState = windowStateKeeper({
    defaultWidth: 1200,
    defaultHeight: 800
  });

  // Create the browser window with enhanced security best practices
  mainWindow = new BrowserWindow({
    x: mainWindowState.x,
    y: mainWindowState.y,
    width: mainWindowState.width,
    height: mainWindowState.height,
    minWidth: 800,
    minHeight: 600,
    show: false, // Don't show until ready
    icon: join(__dirname, '..', '..', 'public', 'icon.png'), // App icon
    webPreferences: {
      nodeIntegration: false, // Security: disable node integration
      contextIsolation: true, // Security: enable context isolation
      enableRemoteModule: false, // Security: disable remote module
      preload: join(__dirname, '..', 'preload', 'preload.js'), // Preload script
      webSecurity: true, // Security: enable web security
      allowRunningInsecureContent: false, // Security: don't allow insecure content
      experimentalFeatures: false, // Security: disable experimental features
      sandbox: false, // Keep false for now to allow preload script access
      nodeIntegrationInWorker: false, // Security: disable node integration in workers
      nodeIntegrationInSubFrames: false, // Security: disable node integration in subframes
      safeDialogs: true, // Security: enable safe dialogs
      safeDialogsMessage: 'This app is trying to show a dialog', // Security message
      navigateOnDragDrop: false, // Security: prevent navigation on drag/drop
      disableBlinkFeatures: 'Auxclick', // Security: disable potentially dangerous features
      enableBlinkFeatures: '' // Security: explicitly set to empty
    }
  });

  // Let windowStateKeeper manage the window
  mainWindowState.manage(mainWindow);

  // Load the React app
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${join(__dirname, '../../build/index.html')}`;

  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Focus on window (in case it's hidden behind other windows)
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Enhanced security: Handle external links with validation
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    if (isUrlSafe(url)) {
      shell.openExternal(url);
    } else {
      console.warn('Blocked unsafe URL:', url);
    }
    return { action: 'deny' };
  });

  // Security: Restrict navigation to safe URLs only
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const currentUrl = mainWindow.webContents.getURL();

    // Allow navigation within the app
    if (navigationUrl.startsWith('file://') && currentUrl.startsWith('file://')) {
      return; // Allow local navigation
    }

    // Allow localhost in development
    if (isDev && navigationUrl.startsWith('http://localhost:3000')) {
      return;
    }

    // Block all other navigation attempts
    console.warn('Blocked navigation to:', navigationUrl);
    event.preventDefault();
  });

  // Security: Handle certificate errors
  mainWindow.webContents.on('certificate-error', (event, url, error, certificate, callback) => {
    if (isDev) {
      // In development, allow self-signed certificates for localhost
      if (url.startsWith('https://localhost') || url.startsWith('https://127.0.0.1')) {
        event.preventDefault();
        callback(true);
        return;
      }
    }

    // In production, always reject certificate errors
    console.error('Certificate error for URL:', url, error);
    callback(false);
  });
}

// App event handlers
app.whenReady().then(async () => {
  try {
    // Initialize database first
    console.log('🔄 Initializing database...');
    db = await databaseManager.initialize(isDev);
    models = new ModelFactory(db);
    console.log('✅ Database and models initialized');

    // Configure session security before creating window
    configureSessionSecurity();

    createWindow();

    // On macOS, re-create window when dock icon is clicked
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
      }
    });
  } catch (error) {
    console.error('❌ Failed to initialize application:', error);
    app.quit();
  }
});

/**
 * Configure session-level security settings
 */
function configureSessionSecurity() {
  const defaultSession = session.defaultSession;

  // Apply security headers
  applySecurityHeaders(defaultSession);

  // Security: Handle permission requests
  defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    const url = webContents.getURL();
    const isAllowed = validatePermissionRequest(permission, url);

    if (isAllowed) {
      console.log(`Granted permission '${permission}' for ${url}`);
    } else {
      console.warn(`Denied permission '${permission}' for ${url}`);
    }

    callback(isAllowed);
  });

  // Security: Handle permission check requests
  defaultSession.setPermissionCheckHandler((webContents, permission, requestingOrigin) => {
    const isAllowed = validatePermissionRequest(permission, requestingOrigin);

    if (!isAllowed) {
      console.warn(`Permission check failed for '${permission}' from ${requestingOrigin}`);
    }

    return isAllowed;
  });

  // Security: Configure CSP for additional protection
  defaultSession.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };

    // Add CSP header if not already present
    if (!responseHeaders['content-security-policy'] && !responseHeaders['Content-Security-Policy']) {
      responseHeaders['Content-Security-Policy'] = [
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
        "style-src 'self' 'unsafe-inline' https://stackpath.bootstrapcdn.com https://cdn.jsdelivr.net https://cdn01.boxcdn.net https://fonts.googleapis.com; " +
        "font-src 'self' https://stackpath.bootstrapcdn.com https://cdn.jsdelivr.net https://fonts.gstatic.com; " +
        "img-src 'self' data: https:; " +
        "connect-src 'self' https:; " +
        "frame-src 'none'; " +
        "object-src 'none'; " +
        "base-uri 'self';"
      ];
    }

    callback({ responseHeaders });
  });

  // Security: Block insecure content
  defaultSession.webRequest.onBeforeRequest((details, callback) => {
    const url = details.url;

    // Block non-HTTPS requests in production (except for local files)
    if (!isDev && url.startsWith('http://') && !url.startsWith('http://localhost')) {
      console.warn('Blocked insecure HTTP request:', url);
      callback({ cancel: true });
      return;
    }

    callback({ cancel: false });
  });
}

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // Close database connection
  if (databaseManager) {
    console.log('🔄 Closing database connection...');
    databaseManager.close();
  }

  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle app quit
app.on('before-quit', () => {
  // Ensure database is closed
  if (databaseManager) {
    databaseManager.close();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// IPC handlers for future database operations
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

// Database operations with security validation
ipcMain.handle('db-get-deals', async (event, options = {}) => {
  try {
    if (!validateIpcMessage('db-get-deals', options)) {
      throw new Error('Invalid IPC message');
    }

    if (!models) {
      throw new Error('Database not initialized');
    }

    return { success: true, data: models.deals.findAll(options) };
  } catch (error) {
    console.error('Database get deals error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-create-deal', async (event, dealData) => {
  try {
    if (!validateIpcMessage('db-create-deal', dealData)) {
      throw new Error('Invalid IPC message');
    }

    if (!models) {
      throw new Error('Database not initialized');
    }

    const deal = models.deals.create(dealData);
    return { success: true, data: deal };
  } catch (error) {
    console.error('Database create deal error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-update-deal', async (event, id, dealData) => {
  try {
    if (!validateIpcMessage('db-update-deal', { id, dealData })) {
      throw new Error('Invalid IPC message');
    }

    if (!models) {
      throw new Error('Database not initialized');
    }

    const deal = models.deals.update(id, dealData);
    return { success: true, data: deal };
  } catch (error) {
    console.error('Database update deal error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-delete-deal', async (event, id) => {
  try {
    if (!validateIpcMessage('db-delete-deal', { id })) {
      throw new Error('Invalid IPC message');
    }

    if (!models) {
      throw new Error('Database not initialized');
    }

    const success = models.deals.delete(id);
    return { success, data: { deleted: success } };
  } catch (error) {
    console.error('Database delete deal error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-search-deals', async (event, criteria) => {
  try {
    if (!validateIpcMessage('db-search-deals', criteria)) {
      throw new Error('Invalid IPC message');
    }

    if (!models) {
      throw new Error('Database not initialized');
    }

    const deals = models.deals.searchDeals(criteria);
    return { success: true, data: deals };
  } catch (error) {
    console.error('Database search deals error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-get-dashboard-data', async (event) => {
  try {
    if (!models) {
      throw new Error('Database not initialized');
    }

    const dashboardData = models.deals.getDashboardData();
    return { success: true, data: dashboardData };
  } catch (error) {
    console.error('Database dashboard data error:', error.message);
    return { success: false, error: error.message };
  }
});

// Reports operations
ipcMain.handle('db-get-reports', async (event, options = {}) => {
  try {
    if (!validateIpcMessage('db-get-reports', options)) {
      throw new Error('Invalid IPC message');
    }

    if (!models) {
      throw new Error('Database not initialized');
    }

    return { success: true, data: models.reports.findAll(options) };
  } catch (error) {
    console.error('Database get reports error:', error.message);
    return { success: false, error: error.message };
  }
});

// Consultants operations
ipcMain.handle('db-get-consultants', async (event, options = {}) => {
  try {
    if (!validateIpcMessage('db-get-consultants', options)) {
      throw new Error('Invalid IPC message');
    }

    if (!models) {
      throw new Error('Database not initialized');
    }

    return { success: true, data: models.consultants.findAll(options) };
  } catch (error) {
    console.error('Database get consultants error:', error.message);
    return { success: false, error: error.message };
  }
});

// Database statistics
ipcMain.handle('db-get-stats', async (event) => {
  try {
    if (!models) {
      throw new Error('Database not initialized');
    }

    const stats = models.getStats();
    return { success: true, data: stats };
  } catch (error) {
    console.error('Database stats error:', error.message);
    return { success: false, error: error.message };
  }
});

// Enhanced File system operations with security validation
ipcMain.handle('fs-write-file', async (event, path, data) => {
  try {
    // Security: Validate IPC message
    if (!validateIpcMessage('fs-write-file', { path, data })) {
      throw new Error('Invalid IPC message');
    }

    // Security: Sanitize file path
    const safePath = sanitizeFilePath(path);
    if (!safePath) {
      throw new Error('Invalid file path');
    }

    await fs.writeFile(safePath, data, 'utf8');
    return { success: true };
  } catch (error) {
    console.error('File write error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs-read-file', async (event, path) => {
  try {
    // Security: Validate IPC message
    if (!validateIpcMessage('fs-read-file', { path })) {
      throw new Error('Invalid IPC message');
    }

    // Security: Sanitize file path
    const safePath = sanitizeFilePath(path);
    if (!safePath) {
      throw new Error('Invalid file path');
    }

    const data = await fs.readFile(safePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    console.error('File read error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs-exists', async (event, path) => {
  try {
    // Security: Validate IPC message
    if (!validateIpcMessage('fs-exists', { path })) {
      return false;
    }

    // Security: Sanitize file path
    const safePath = sanitizeFilePath(path);
    if (!safePath) {
      return false;
    }

    await fs.access(safePath);
    return true;
  } catch {
    return false;
  }
});

ipcMain.handle('fs-mkdir', async (event, path) => {
  try {
    // Security: Validate IPC message
    if (!validateIpcMessage('fs-mkdir', { path })) {
      throw new Error('Invalid IPC message');
    }

    // Security: Sanitize file path
    const safePath = sanitizeFilePath(path);
    if (!safePath) {
      throw new Error('Invalid file path');
    }

    await fs.mkdir(safePath, { recursive: true });
    return { success: true };
  } catch (error) {
    console.error('Directory creation error:', error.message);
    return { success: false, error: error.message };
  }
});

// Clipboard operations
ipcMain.handle('clipboard-write-text', (event, text) => {
  clipboard.writeText(text);
  return true;
});

ipcMain.handle('clipboard-read-text', () => {
  return clipboard.readText();
});

// Window operations
ipcMain.handle('window-minimize', () => {
  if (mainWindow) mainWindow.minimize();
});

ipcMain.handle('window-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('window-close', () => {
  if (mainWindow) mainWindow.close();
});

ipcMain.handle('window-is-maximized', () => {
  return mainWindow ? mainWindow.isMaximized() : false;
});

ipcMain.handle('window-set-title', (event, title) => {
  if (mainWindow) mainWindow.setTitle(title);
});

// Auto-updater events (for production)
if (!isDev) {
  const { autoUpdater } = require('electron-updater');

  autoUpdater.checkForUpdatesAndNotify();

  autoUpdater.on('update-available', () => {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Update available',
      message: 'A new version is available. It will be downloaded in the background.',
      buttons: ['OK']
    });
  });

  autoUpdater.on('update-downloaded', () => {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Update ready',
      message: 'Update downloaded. The application will restart to apply the update.',
      buttons: ['Restart', 'Later']
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  });
}
