# Task ID: 7
# Title: Implement Basic CRUD Operations
# Status: pending
# Dependencies: 6
# Priority: high
# Description: Develop basic Create, Read, Update, and Delete operations for the local database using a JSON-based schema.
# Details:
Migrate the current SQLite database schema to mirror the production PostgreSQL structure using JSONB fields. Implement CRUD operations that work with this simplified JSON-based schema, focusing on the deal and tracker_data fields. Ensure that these operations are efficient and reliable, supporting dynamic fields and complex field processing logic.

# Test Strategy:
Perform CRUD operations on the JSON-based schema and verify that they work correctly without data loss or corruption. Ensure that dynamic fields and complex processing logic are handled accurately, focusing on the deal and tracker_data fields.

# Subtasks:
## 8. Migrate SQLite Schema to JSON-based Structure [pending]
### Dependencies: None
### Description: Update the SQLite schema to use JSONB fields for deal and tracker_data. Implement generated columns for indexing key fields like order_id.
### Details:


## 9. Implement Models for JSON Data Structure [pending]
### Dependencies: None
### Description: Develop models that work with the JSON data structure, ensuring compatibility with the dynamic field definitions, focusing on deal and tracker_data.
### Details:


## 10. Create CRUD Operations for JSON Schema [pending]
### Dependencies: None
### Description: Implement Create, Read, Update, and Delete operations that handle the JSON-based schema, focusing on the deal and tracker_data fields.
### Details:


## 11. Build Tracker Detail Functionality [pending]
### Dependencies: None
### Description: Develop functionality to handle tracker updates within the JSON schema, focusing on the tracker_data field.
### Details:


## 12. Ensure Complex Field Processing Logic [pending]
### Dependencies: None
### Description: Implement support for complex field processing logic as used in the production environment, focusing on the deal and tracker_data fields.
### Details:


