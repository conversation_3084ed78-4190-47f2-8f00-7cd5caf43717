/**
 * Deals model for database operations
 * Handles deal-specific business logic and queries
 */

const BaseModel = require('./base-model');

class DealsModel extends BaseModel {
  constructor(db) {
    super(db, 'deals');
    this.initializeDealsStatements();
  }

  /**
   * Initialize deal-specific prepared statements
   */
  initializeDealsStatements() {
    this.preparedStatements.findByStatus = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE status = ? ORDER BY created_at DESC
    `);

    this.preparedStatements.findByDateRange = this.db.prepare(`
      SELECT * FROM ${this.tableName} 
      WHERE loan_closing_date BETWEEN ? AND ?
      ORDER BY loan_closing_date ASC
    `);

    this.preparedStatements.findUpcoming = this.db.prepare(`
      SELECT * FROM ${this.tableName} 
      WHERE reviewed_needed_date >= date('now')
      ORDER BY reviewed_needed_date ASC
    `);

    this.preparedStatements.findOverdue = this.db.prepare(`
      SELECT * FROM ${this.tableName} 
      WHERE reviewed_needed_date < date('now') AND status != 'Closed'
      ORDE<PERSON> BY reviewed_needed_date ASC
    `);

    this.preparedStatements.updateStatus = this.db.prepare(`
      UPDATE ${this.tableName} 
      SET status = ?, updated_at = CURRENT_TIMESTAMP, sync_status = 'pending'
      WHERE id = ?
    `);
  }

  /**
   * Find deals by status
   * @param {string} status - Deal status
   * @returns {Array} Array of deals
   */
  findByStatus(status) {
    try {
      return this.preparedStatements.findByStatus.all(status);
    } catch (error) {
      console.error('Error finding deals by status:', error);
      throw error;
    }
  }

  /**
   * Find deals by date range
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @returns {Array} Array of deals
   */
  findByDateRange(startDate, endDate) {
    try {
      return this.preparedStatements.findByDateRange.all(startDate, endDate);
    } catch (error) {
      console.error('Error finding deals by date range:', error);
      throw error;
    }
  }

  /**
   * Find upcoming deals (review needed date >= today)
   * @returns {Array} Array of upcoming deals
   */
  findUpcoming() {
    try {
      return this.preparedStatements.findUpcoming.all();
    } catch (error) {
      console.error('Error finding upcoming deals:', error);
      throw error;
    }
  }

  /**
   * Find overdue deals (review needed date < today and not closed)
   * @returns {Array} Array of overdue deals
   */
  findOverdue() {
    try {
      return this.preparedStatements.findOverdue.all();
    } catch (error) {
      console.error('Error finding overdue deals:', error);
      throw error;
    }
  }

  /**
   * Update deal status
   * @param {number} id - Deal ID
   * @param {string} status - New status
   * @returns {Object} Updated deal
   */
  updateStatus(id, status) {
    try {
      const result = this.preparedStatements.updateStatus.run(status, id);
      
      if (result.changes === 0) {
        throw new Error(`No deal found with ID: ${id}`);
      }

      // Queue operation for sync
      this.queueOperation('UPDATE', id, { status });

      return this.findById(id);
    } catch (error) {
      console.error('Error updating deal status:', error);
      throw error;
    }
  }

  /**
   * Create deal with validation
   * @param {Object} dealData - Deal data
   * @returns {Object} Created deal
   */
  create(dealData) {
    try {
      // Validate required fields
      this.validateDealData(dealData);

      // Set default status if not provided
      if (!dealData.status) {
        dealData.status = 'Active';
      }

      return super.create(dealData);
    } catch (error) {
      console.error('Error creating deal:', error);
      throw error;
    }
  }

  /**
   * Update deal with validation
   * @param {number} id - Deal ID
   * @param {Object} dealData - Updated deal data
   * @returns {Object} Updated deal
   */
  update(id, dealData) {
    try {
      // Validate data if provided
      if (Object.keys(dealData).length > 0) {
        this.validateDealData(dealData, false); // Partial validation for updates
      }

      return super.update(id, dealData);
    } catch (error) {
      console.error('Error updating deal:', error);
      throw error;
    }
  }

  /**
   * Validate deal data
   * @param {Object} data - Deal data to validate
   * @param {boolean} isCreate - Whether this is for creation (requires all fields)
   */
  validateDealData(data, isCreate = true) {
    const errors = [];

    // Required fields for creation
    if (isCreate) {
      if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
        errors.push('Deal name is required');
      }
    }

    // Validate status if provided
    if (data.status) {
      const validStatuses = ['Active', 'On-hold', 'Closed', 'Dead', 'Paid', 'Archive'];
      if (!validStatuses.includes(data.status)) {
        errors.push(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
      }
    }

    // Validate dates if provided
    if (data.loan_closing_date && !this.isValidDate(data.loan_closing_date)) {
      errors.push('Invalid loan closing date format');
    }

    if (data.reviewed_needed_date && !this.isValidDate(data.reviewed_needed_date)) {
      errors.push('Invalid review needed date format');
    }

    // Validate loan amounts if provided
    if (data.mortgage_loan_amount && !this.isValidAmount(data.mortgage_loan_amount)) {
      errors.push('Invalid mortgage loan amount');
    }

    if (errors.length > 0) {
      throw new Error(`Validation errors: ${errors.join(', ')}`);
    }
  }

  /**
   * Check if date string is valid
   * @param {string} dateString - Date string to validate
   * @returns {boolean} Whether date is valid
   */
  isValidDate(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  /**
   * Check if amount string is valid
   * @param {string} amount - Amount string to validate
   * @returns {boolean} Whether amount is valid
   */
  isValidAmount(amount) {
    if (!amount) return true; // Optional field
    // Remove commas and dollar signs, then check if it's a valid number
    const cleanAmount = amount.replace(/[$,]/g, '');
    return !isNaN(parseFloat(cleanAmount)) && isFinite(cleanAmount);
  }

  /**
   * Get deals dashboard data
   * @returns {Object} Dashboard statistics
   */
  getDashboardData() {
    try {
      const totalDeals = this.count();
      const activeDeals = this.findByStatus('Active').length;
      const upcomingDeals = this.findUpcoming().length;
      const overdueDeals = this.findOverdue().length;

      // Get recent deals (last 10)
      const recentDeals = this.findAll({ limit: 10 });

      return {
        totalDeals,
        activeDeals,
        upcomingDeals,
        overdueDeals,
        recentDeals
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Search deals by multiple criteria
   * @param {Object} criteria - Search criteria
   * @returns {Array} Matching deals
   */
  searchDeals(criteria) {
    try {
      const { searchTerm, status, dateFrom, dateTo, propertyType } = criteria;
      
      let query = `SELECT * FROM ${this.tableName} WHERE 1=1`;
      const params = [];

      // Text search
      if (searchTerm) {
        query += ` AND (name LIKE ? OR borrower_sponsor_name LIKE ? OR property_name LIKE ?)`;
        const searchPattern = `%${searchTerm}%`;
        params.push(searchPattern, searchPattern, searchPattern);
      }

      // Status filter
      if (status) {
        query += ` AND status = ?`;
        params.push(status);
      }

      // Date range filter
      if (dateFrom) {
        query += ` AND loan_closing_date >= ?`;
        params.push(dateFrom);
      }

      if (dateTo) {
        query += ` AND loan_closing_date <= ?`;
        params.push(dateTo);
      }

      // Property type filter
      if (propertyType) {
        query += ` AND property_type = ?`;
        params.push(propertyType);
      }

      query += ` ORDER BY created_at DESC`;

      const stmt = this.db.prepare(query);
      return stmt.all(...params);
    } catch (error) {
      console.error('Error searching deals:', error);
      throw error;
    }
  }
}

module.exports = DealsModel;
