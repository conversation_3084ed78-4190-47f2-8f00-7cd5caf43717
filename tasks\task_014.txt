# Task ID: 14
# Title: Implement Dual-Expiration Token System
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Develop a token system for authentication in both online and offline modes.
# Details:
Create a hybrid JWT authentication system with standard online expiration and extended offline expiration. Store tokens securely using electron-store with encryption.

# Test Strategy:
Test the token system to ensure it functions correctly in both online and offline scenarios.
