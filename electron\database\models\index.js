/**
 * Database models index
 * Exports all model classes and provides model factory
 */

const BaseModel = require('./base-model');
const DealsModel = require('./deals-model');
const UsersModel = require('./users-model');
const ReportsModel = require('./reports-model');
const ConsultantsModel = require('./consultants-model');

class ModelFactory {
  constructor(db) {
    this.db = db;
    this.models = {};
    this.initializeModels();
  }

  /**
   * Initialize all models
   */
  initializeModels() {
    this.models.deals = new DealsModel(this.db);
    this.models.users = new UsersModel(this.db);
    this.models.reports = new ReportsModel(this.db);
    this.models.consultants = new ConsultantsModel(this.db);
  }

  /**
   * Get model by name
   * @param {string} modelName - Model name
   * @returns {BaseModel} Model instance
   */
  getModel(modelName) {
    if (!this.models[modelName]) {
      throw new Error(`Model '${modelName}' not found`);
    }
    return this.models[modelName];
  }

  /**
   * Get all models
   * @returns {Object} All model instances
   */
  getAllModels() {
    return this.models;
  }

  /**
   * Get deals model
   * @returns {DealsModel} Deals model instance
   */
  get deals() {
    return this.models.deals;
  }

  /**
   * Get users model
   * @returns {UsersModel} Users model instance
   */
  get users() {
    return this.models.users;
  }

  /**
   * Get reports model
   * @returns {ReportsModel} Reports model instance
   */
  get reports() {
    return this.models.reports;
  }

  /**
   * Get consultants model
   * @returns {ConsultantsModel} Consultants model instance
   */
  get consultants() {
    return this.models.consultants;
  }

  /**
   * Add new model dynamically (for schema discovery)
   * @param {string} modelName - Model name
   * @param {string} tableName - Table name
   * @returns {BaseModel} New model instance
   */
  addModel(modelName, tableName) {
    this.models[modelName] = new BaseModel(this.db, tableName);
    return this.models[modelName];
  }

  /**
   * Check if model exists
   * @param {string} modelName - Model name
   * @returns {boolean} Whether model exists
   */
  hasModel(modelName) {
    return !!this.models[modelName];
  }

  /**
   * Get model names
   * @returns {Array} Array of model names
   */
  getModelNames() {
    return Object.keys(this.models);
  }

  /**
   * Execute transaction across multiple models
   * @param {Function} callback - Transaction callback
   * @returns {any} Transaction result
   */
  transaction(callback) {
    const transaction = this.db.transaction(callback);
    return transaction();
  }

  /**
   * Get database statistics
   * @returns {Object} Database statistics
   */
  getStats() {
    const stats = {};
    
    for (const [modelName, model] of Object.entries(this.models)) {
      try {
        stats[modelName] = {
          total: model.count(),
          pending: model.findPending().length
        };
      } catch (error) {
        console.error(`Error getting stats for ${modelName}:`, error);
        stats[modelName] = { total: 0, pending: 0, error: error.message };
      }
    }

    return stats;
  }

  /**
   * Clear all data (for testing/reset)
   * @param {boolean} confirm - Confirmation flag
   */
  clearAllData(confirm = false) {
    if (!confirm) {
      throw new Error('Must confirm data clearing with confirm=true');
    }

    const transaction = this.db.transaction(() => {
      for (const modelName of Object.keys(this.models)) {
        this.db.exec(`DELETE FROM ${modelName}`);
      }
      // Clear operation queue
      this.db.exec('DELETE FROM operation_queue');
      // Reset sync metadata
      this.db.exec('UPDATE sync_metadata SET last_sync_timestamp = NULL, total_records = 0');
    });

    transaction();
    console.log('✅ All data cleared');
  }
}

module.exports = {
  BaseModel,
  DealsModel,
  UsersModel,
  ReportsModel,
  ConsultantsModel,
  ModelFactory
};
