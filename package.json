{"name": "risk-app", "version": "0.1.0", "private": true, "dependencies": {"@matharumanpreet00/react-daterange-picker": "^1.0.5", "@reduxjs/toolkit": "^1.6.2", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-redux": "^7.1.18", "@types/react-router-dom": "^5.3.0", "@types/react-select": "^5.0.1", "axios": "^0.25.0", "better-sqlite3": "^11.10.0", "bootstrap": "^5.1.3", "date-fns": "^2.29.3", "dayjs": "^1.11.0", "formik": "^2.0.0", "framer-motion": "^4.1.17", "react": "^18.2.0", "react-data-grid": "^7.0.0-beta.20", "react-date-range": "^1.4.0", "react-dom": "^18.2.0", "react-error-boundary": "^3.1.3", "react-is": "^18.2.0", "react-paginate": "^8.1.4", "react-query": "^3.25.1", "react-redux": "^7.2.5", "react-router-dom": "^5.0.0", "react-scripts": "5.0.0", "react-select": "^5.1.0", "react-table": "^7.7.0", "react-toastify": "^8.0.3", "sass": "^1.52.1", "typescript": "^4.1.2", "web-vitals": "^1.0.1", "xlsx": "^0.18.5", "yup": "^0.32.9"}, "resolutions": {"@types/react": "17.0.2", "@types/react-dom": "17.0.2"}, "main": "electron/main/main.js", "homepage": "./", "scripts": {"react-start": "react-scripts start", "react-build": "react-scripts build", "react-test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm run react-start\" \"wait-on http://localhost:3000 && electron .\"", "start": "npm run electron-dev", "build": "npm run react-build && electron-builder", "build-electron": "electron-builder", "dist": "npm run react-build && electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "security-test": "electron scripts/run-security-tests.js", "test-security": "npm run security-test", "database-test": "electron scripts/test-database.js", "test-database": "npm run database-test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-object-rest-spread": "^7.27.2", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "electron": "^36.3.1", "electron-builder": "^26.0.12", "electron-devtools-installer": "^4.0.0", "electron-is-dev": "^3.0.1", "electron-reload": "^2.0.0-alpha.1", "electron-store": "^10.0.1", "electron-window-state": "^5.0.3", "install-peers": "^1.0.4", "wait-on": "^8.0.3", "webpack-cli": "^6.0.1"}, "build": {"appId": "com.riskapp.desktop", "productName": "Risk Management App", "directories": {"output": "dist"}, "files": ["build/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.business"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}