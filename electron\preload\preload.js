const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * Validate input parameters for security
 * @param {any} value - Value to validate
 * @param {string} type - Expected type
 * @returns {boolean} - True if valid
 */
function validateInput(value, type) {
  if (type === 'string') {
    return typeof value === 'string' && value.length > 0 && value.length < 10000;
  }
  if (type === 'object') {
    return typeof value === 'object' && value !== null;
  }
  return typeof value === type;
}

/**
 * Secure IPC invoke wrapper with validation
 * @param {string} channel - IPC channel
 * @param {...any} args - Arguments to send
 * @returns {Promise} - IPC response
 */
async function secureInvoke(channel, ...args) {
  // Validate channel name
  if (typeof channel !== 'string' || !/^[a-zA-Z0-9-_]+$/.test(channel)) {
    throw new Error('Invalid IPC channel');
  }

  // Validate arguments size
  const argsString = JSON.stringify(args);
  if (argsString.length > 1024 * 1024) { // 1MB limit
    throw new Error('IPC message too large');
  }

  return ipcRenderer.invoke(channel, ...args);
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getVersion: () => ipcRenderer.invoke('app-version'),
  getPlatform: () => process.platform,
  getArch: () => process.arch,

  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Menu events (listen only)
  onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),
  onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),
  onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),

  // Database operations (will be added later)
  database: {
    // Placeholder for database operations
  },

  // Network status
  onOnline: (callback) => {
    window.addEventListener('online', callback);
    return () => window.removeEventListener('online', callback);
  },

  onOffline: (callback) => {
    window.addEventListener('offline', callback);
    return () => window.removeEventListener('offline', callback);
  },

  // File system operations with security validation
  fileSystem: {
    writeFile: (path, data) => {
      if (!validateInput(path, 'string') || !validateInput(data, 'string')) {
        return Promise.reject(new Error('Invalid file system parameters'));
      }
      return secureInvoke('fs-write-file', path, data);
    },
    readFile: (path) => {
      if (!validateInput(path, 'string')) {
        return Promise.reject(new Error('Invalid file path'));
      }
      return secureInvoke('fs-read-file', path);
    },
    exists: (path) => {
      if (!validateInput(path, 'string')) {
        return Promise.reject(new Error('Invalid file path'));
      }
      return secureInvoke('fs-exists', path);
    },
    mkdir: (path) => {
      if (!validateInput(path, 'string')) {
        return Promise.reject(new Error('Invalid directory path'));
      }
      return secureInvoke('fs-mkdir', path);
    },
  },

  // Clipboard operations
  clipboard: {
    writeText: (text) => ipcRenderer.invoke('clipboard-write-text', text),
    readText: () => ipcRenderer.invoke('clipboard-read-text'),
  },

  // Window operations
  window: {
    minimize: () => ipcRenderer.invoke('window-minimize'),
    maximize: () => ipcRenderer.invoke('window-maximize'),
    close: () => ipcRenderer.invoke('window-close'),
    isMaximized: () => ipcRenderer.invoke('window-is-maximized'),
    setTitle: (title) => ipcRenderer.invoke('window-set-title', title),
  },

  // Authentication (will be added later)
  auth: {
    // Placeholder for authentication operations
  },

  // Sync operations (will be added later)
  sync: {
    // Placeholder for sync operations
  }
});

// Security: Remove access to Node.js APIs in the renderer process
delete window.require;
delete window.exports;
delete window.module;
