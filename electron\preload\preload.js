const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getVersion: () => ipcRenderer.invoke('app-version'),
  getPlatform: () => process.platform,
  getArch: () => process.arch,

  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Menu events (listen only)
  onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),
  onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),
  onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),

  // Database operations (will be added later)
  database: {
    // Placeholder for database operations
  },

  // Network status
  onOnline: (callback) => {
    window.addEventListener('online', callback);
    return () => window.removeEventListener('online', callback);
  },

  onOffline: (callback) => {
    window.addEventListener('offline', callback);
    return () => window.removeEventListener('offline', callback);
  },

  // File system operations
  fileSystem: {
    writeFile: (path, data) => ipcRenderer.invoke('fs-write-file', path, data),
    readFile: (path) => ipcRenderer.invoke('fs-read-file', path),
    exists: (path) => ipcRenderer.invoke('fs-exists', path),
    mkdir: (path) => ipcRenderer.invoke('fs-mkdir', path),
  },

  // Clipboard operations
  clipboard: {
    writeText: (text) => ipcRenderer.invoke('clipboard-write-text', text),
    readText: () => ipcRenderer.invoke('clipboard-read-text'),
  },

  // Window operations
  window: {
    minimize: () => ipcRenderer.invoke('window-minimize'),
    maximize: () => ipcRenderer.invoke('window-maximize'),
    close: () => ipcRenderer.invoke('window-close'),
    isMaximized: () => ipcRenderer.invoke('window-is-maximized'),
    setTitle: (title) => ipcRenderer.invoke('window-set-title', title),
  },

  // Authentication (will be added later)
  auth: {
    // Placeholder for authentication operations
  },

  // Sync operations (will be added later)
  sync: {
    // Placeholder for sync operations
  }
});

// Security: Remove access to Node.js APIs in the renderer process
delete window.require;
delete window.exports;
delete window.module;
